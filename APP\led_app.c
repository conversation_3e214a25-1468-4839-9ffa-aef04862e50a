/* Licence
 * Company: MCUSTUDIO
 * Auther: Ahypnis.
 * Version: V0.10
 * Time: 2025/06/05
 * Note:
 */
#include "mcu_cmic_gd32f470vet6.h"
#include "system_state.h"
#include <string.h>

uint8_t ucLed[6] = {0, 0, 0, 0, 0, 0}; // LED状态数组

/**
 * @brief 显示或关闭LED
 * @param ucLed LED数据存储数组
 */
void led_disp(uint8_t *ucLed)
{
    // 用于记录当前LED状态的临时变量
    uint8_t temp = 0x00;
    // 记录之前LED状态的变量，用于判断是否需要更新显示
    static uint8_t temp_old = 0xff;

    for (int i = 0; i < 6; i++)
    {
        // 将LED状态合并到temp变量中，便于后续比较
        if (ucLed[i])
            temp |= (1 << i); // 设置第i位为1
    }

    // 仅当前状态与之前状态不同时，才更新显示
    if (temp_old != temp)
    {
        // 根据GPIO位设置对应的LED状态
        LED1_SET(temp & 0x01);
        LED2_SET(temp & 0x02);
        LED3_SET(temp & 0x04);
        LED4_SET(temp & 0x08);
        LED5_SET(temp & 0x10);
        LED6_SET(temp & 0x20);

        // 更新旧状态
        temp_old = temp;
    }
}

/**
 * @brief LED状态指示任务
 * 根据系统工作模式控制LED显示：LED1-采集模式，LED2-存储模式，LED3-暂停模式
 */
void led_task(void)
{
    // 获取当前工作模式
    system_mode_t mode = system_mode_get();

    // 清空LED数组
    memset(ucLed, 0, sizeof(ucLed));

    // 根据模式设置对应LED
    switch (mode)
    {
    case SYSTEM_MODE_COLLECT:
        ucLed[0] = 1; // LED1亮 - 采集模式
        break;
    case SYSTEM_MODE_STORE:
        ucLed[1] = 1; // LED2亮 - 存储模式
        break;
    case SYSTEM_MODE_PAUSE:
        ucLed[2] = 1; // LED3亮 - 暂停模式
        break;
    default:
        // 默认情况下所有LED熄灭
        break;
    }

    // 更新LED显示
    led_disp(ucLed);
}
