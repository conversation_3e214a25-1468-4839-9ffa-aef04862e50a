#include "mcu_cmic_gd32f470vet6.h"
#include <stdio.h>

extern rtc_parameter_struct rtc_initpara;

// BCD码转十进制
static uint8_t bcd_to_dec(uint8_t bcd)
{
    return ((bcd >> 4) * 10) + (bcd & 0x0F);
}

// 十进制转BCD码
static uint8_t dec_to_bcd(uint8_t dec)
{
    return ((dec / 10) << 4) | (dec % 10);
}

/*!
    \brief      RTC任务：维护时间精度和监控
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_task(void)
{
    static uint32_t last_check = 0;
    static uint32_t error_count = 0;
    uint32_t current_time = get_system_ms();

    // 每500ms检查一次RTC状态
    if (current_time - last_check < 500)
    {
        return;
    }
    last_check = current_time;

    // 获取当前RTC时间
    if (SUCCESS != rtc_current_time_get(&rtc_initpara))
    {
        error_count++;
        // 如果连续错误超过10次，报告RTC故障
        if (error_count > 10)
        {
            // 可以在这里添加RTC故障处理逻辑
            error_count = 0; // 重置计数器
        }
    }
    else
    {
        error_count = 0; // 重置错误计数器
    }
}

/**
 * @brief 将RTC时间格式化为标准字符串格式
 * @param buffer 输出缓冲区
 * @param size 缓冲区大小
 * @return 格式化后的字符串长度
 */
int rtc_time_format(char *buffer, size_t size)
{
    rtc_current_time_get(&rtc_initpara);

    // 将BCD码转换为十进制
    uint8_t year = bcd_to_dec(rtc_initpara.year);
    uint8_t month = bcd_to_dec(rtc_initpara.month);
    uint8_t date = bcd_to_dec(rtc_initpara.date);
    uint8_t hour = bcd_to_dec(rtc_initpara.hour);
    uint8_t minute = bcd_to_dec(rtc_initpara.minute);
    uint8_t second = bcd_to_dec(rtc_initpara.second);

    // 格式化为 "YYYY-MM-DD HH:MM:SS" 格式
    return snprintf(buffer, size, "20%02d-%02d-%02d %02d:%02d:%02d",
                    year, month, date, hour, minute, second);
}

/**
 * @brief 计算星期几 (基于Zeller公式)
 * @param year 年份 (完整年份，如2025)
 * @param month 月份 (1-12)
 * @param date 日期 (1-31)
 * @return 星期几 (0=周日, 1=周一, ..., 6=周六)
 */
static uint8_t calculate_day_of_week(uint16_t year, uint8_t month, uint8_t date)
{
    if (month < 3)
    {
        month += 12;
        year--;
    }

    uint8_t k = year % 100;
    uint8_t j = year / 100;

    uint8_t h = (date + (13 * (month + 1)) / 5 + k + k / 4 + j / 4 - 2 * j) % 7;

    // 转换为周一=1的格式
    return (h + 5) % 7 + 1;
}

/**
 * @brief 设置RTC时间
 * @param year 年份 (0-99，表示20xx年)
 * @param month 月份 (1-12)
 * @param date 日期 (1-31)
 * @param hour 小时 (0-23)
 * @param minute 分钟 (0-59)
 * @param second 秒钟 (0-59)
 * @return 0-成功，-1-参数错误，-2-RTC设置失败
 */
int rtc_time_set(uint8_t year, uint8_t month, uint8_t date,
                 uint8_t hour, uint8_t minute, uint8_t second)
{
    // 参数范围检查
    if (year > 99 || month < 1 || month > 12 || date < 1 || date > 31 ||
        hour > 23 || minute > 59 || second > 59)
    {
        return -1; // 参数错误
    }

    // 更详细的日期验证
    uint8_t days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    // 闰年检查
    uint16_t full_year = 2000 + year;
    if ((full_year % 4 == 0 && full_year % 100 != 0) || (full_year % 400 == 0))
    {
        days_in_month[1] = 29; // 闰年2月有29天
    }

    if (date > days_in_month[month - 1])
    {
        return -1; // 日期超出该月最大天数
    }

    // 计算星期几
    uint8_t day_of_week = calculate_day_of_week(full_year, month, date);

    // 设置时间参数 (转换为BCD码)
    rtc_initpara.year = dec_to_bcd(year);
    rtc_initpara.month = dec_to_bcd(month);
    rtc_initpara.date = dec_to_bcd(date);
    rtc_initpara.hour = dec_to_bcd(hour);
    rtc_initpara.minute = dec_to_bcd(minute);
    rtc_initpara.second = dec_to_bcd(second);

    // 设置星期几 (RTC_MONDAY=1, RTC_TUESDAY=2, ...)
    rtc_initpara.day_of_week = day_of_week;
    rtc_initpara.display_format = RTC_24HOUR;
    rtc_initpara.am_pm = RTC_AM;

    // 调用RTC初始化函数设置时间
    if (ERROR == rtc_init(&rtc_initpara))
    {
        return -2; // RTC设置失败
    }

    return 0; // 设置成功
}
