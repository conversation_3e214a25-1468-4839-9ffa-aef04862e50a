#include "mcu_cmic_gd32f470vet6.h"
#include <stdio.h>

extern rtc_parameter_struct rtc_initpara;

// BCD码转十进制
static uint8_t bcd_to_dec(uint8_t bcd)
{
    return ((bcd >> 4) * 10) + (bcd & 0x0F);
}

// 十进制转BCD码
static uint8_t dec_to_bcd(uint8_t dec)
{
    return ((dec / 10) << 4) | (dec % 10);
}

/*!
    \brief      display the current time
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_task(void)
{
    rtc_current_time_get(&rtc_initpara);

//    oled_printf(0, 3, "%0.2x:%0.2x:%0.2x", rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
}

/**
 * @brief 将RTC时间格式化为标准字符串格式
 * @param buffer 输出缓冲区
 * @param size 缓冲区大小
 * @return 格式化后的字符串长度
 */
int rtc_time_format(char *buffer, size_t size)
{
    rtc_current_time_get(&rtc_initpara);

    // 将BCD码转换为十进制
    uint8_t year = bcd_to_dec(rtc_initpara.year);
    uint8_t month = bcd_to_dec(rtc_initpara.month);
    uint8_t date = bcd_to_dec(rtc_initpara.date);
    uint8_t hour = bcd_to_dec(rtc_initpara.hour);
    uint8_t minute = bcd_to_dec(rtc_initpara.minute);
    uint8_t second = bcd_to_dec(rtc_initpara.second);

    // 格式化为 "YYYY-MM-DD HH:MM:SS" 格式
    return snprintf(buffer, size, "20%02d-%02d-%02d %02d:%02d:%02d",
                    year, month, date, hour, minute, second);
}

/**
 * @brief 设置RTC时间
 * @param year 年份 (0-99)
 * @param month 月份 (1-12)
 * @param date 日期 (1-31)
 * @param hour 小时 (0-23)
 * @param minute 分钟 (0-59)
 * @param second 秒钟 (0-59)
 * @return 0-成功，-1-失败
 */
int rtc_time_set(uint8_t year, uint8_t month, uint8_t date,
                 uint8_t hour, uint8_t minute, uint8_t second)
{
    // 参数范围检查
    if (year > 99 || month < 1 || month > 12 || date < 1 || date > 31 ||
        hour > 23 || minute > 59 || second > 59)
    {
        return -1; // 参数错误
    }

    // 设置时间参数 (转换为BCD码)
    rtc_initpara.year = dec_to_bcd(year);
    rtc_initpara.month = dec_to_bcd(month);
    rtc_initpara.date = dec_to_bcd(date);
    rtc_initpara.hour = dec_to_bcd(hour);
    rtc_initpara.minute = dec_to_bcd(minute);
    rtc_initpara.second = dec_to_bcd(second);

    // 设置其他必要参数
    rtc_initpara.day_of_week = RTC_MONDAY; // 默认设置为周一
    rtc_initpara.display_format = RTC_24HOUR;
    rtc_initpara.am_pm = RTC_AM;

    // 调用RTC初始化函数设置时间
    if (ERROR == rtc_init(&rtc_initpara))
    {
        return -1; // 设置失败
    }

    return 0; // 设置成功
}
