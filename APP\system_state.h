/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/11
* Note: 系统状态管理模块头文件
*/

#ifndef __SYSTEM_STATE_H_
#define __SYSTEM_STATE_H_

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

// 系统工作模式枚举
typedef enum {
    SYSTEM_MODE_COLLECT = 0,  // 采集模式
    SYSTEM_MODE_STORE,        // 存储模式  
    SYSTEM_MODE_PAUSE         // 暂停模式
} system_mode_t;

// 采样频率枚举
typedef enum {
    SAMPLE_FREQ_10HZ = 100,   // 10Hz对应100ms周期
    SAMPLE_FREQ_100HZ = 10    // 100Hz对应10ms周期
} sample_freq_t;

// 系统状态结构体
typedef struct {
    system_mode_t mode;       // 当前工作模式
    sample_freq_t frequency;  // 当前采样频率
    uint8_t mode_changed;     // 模式变化标志
    uint8_t freq_changed;     // 频率变化标志
} system_state_t;

// 函数声明
void system_state_init(void);                                    // 系统状态初始化
system_mode_t system_mode_get(void);                            // 获取当前工作模式
void system_mode_set(system_mode_t mode);                       // 设置工作模式
sample_freq_t system_frequency_get(void);                       // 获取当前采样频率
void system_frequency_set(sample_freq_t freq);                  // 设置采样频率
uint8_t system_mode_is_changed(void);                           // 检查模式是否变化
uint8_t system_frequency_is_changed(void);                      // 检查频率是否变化
void system_state_clear_flags(void);                            // 清除变化标志
const char* system_mode_to_string(system_mode_t mode);          // 模式转字符串
const char* system_frequency_to_string(sample_freq_t freq);     // 频率转字符串

#ifdef __cplusplus
}
#endif

#endif /* __SYSTEM_STATE_H_ */
