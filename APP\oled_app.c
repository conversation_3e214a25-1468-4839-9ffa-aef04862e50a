/* Licence
 * Company: MCUSTUDIO
 * Auther: Ahypnis.
 * Version: V0.10
 * Time: 2025/06/05
 * Note:
 */

#include "mcu_cmic_gd32f470vet6.h"
#include "adc_app.h"
#include "rtc_app.h"
#include "system_state.h"
#include <string.h>

extern uint16_t adc_value[1];

/**
 * @brief 使用类似printf的方式显示字符串，显示6x8像素ASCII字符
 * @param x  X轴字符位置，范围：0-127
 * @param y  Y轴字符位置，范围：0-3
 * 例如：oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // 临时存储格式化后的字符串
  va_list arg;      // 可变参数列表
  int len;          // 返回字符串长度

  va_start(arg, format);
  // 安全地格式化字符串到buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

void oled_task(void)
{
  char time_str[32];
  static uint32_t last_update = 0;
  uint32_t current_time = get_system_ms();

  // 每200ms更新一次显示，避免频繁刷新
  if (current_time - last_update < 200)
  {
    return;
  }
  last_update = current_time;

  // 清除显示缓存，避免重叠显示
  OLED_Clear();

  // 第0行：显示电压值(最多显示15字符)
  oled_printf(0, 0, "V:%.2fV", adc_get_voltage());

  // 第1行：显示时间(只显示时分秒，最多显示15字符)
  if (rtc_time_format(time_str, sizeof(time_str)) > 0)
  {
    // 提取时分秒部分 (格式: YYYY-MM-DD HH:MM:SS)
    char *time_part = strstr(time_str, " ");
    if (time_part && strlen(time_part) >= 9)
    {
      // 只显示时分秒，格式: T:HH:MM:SS (9字符)
      oled_printf(0, 1, "T:%s", time_part + 1);
    }
    else
    {
      oled_printf(0, 1, "T:--:--:--");
    }
  }
  else
  {
    oled_printf(0, 1, "T:--:--:--");
  }

  // 第2行：显示频率(最多显示15字符)
  sample_freq_t freq = system_frequency_get();
  if (freq == SAMPLE_FREQ_10HZ)
  {
    oled_printf(0, 2, "F:10Hz");
  }
  else
  {
    oled_printf(0, 2, "F:100Hz");
  }

  // 第3行：显示模式(使用简短标识，最多显示15字符)
  system_mode_t mode = system_mode_get();
  switch (mode)
  {
  case SYSTEM_MODE_COLLECT:
    oled_printf(0, 3, "M:COL");
    break;
  case SYSTEM_MODE_STORE:
    oled_printf(0, 3, "M:STO");
    break;
  case SYSTEM_MODE_PAUSE:
    oled_printf(0, 3, "M:PAU");
    break;
  default:
    oled_printf(0, 3, "M:ERR");
    break;
  }
}

/* CUSTOM EDIT */
