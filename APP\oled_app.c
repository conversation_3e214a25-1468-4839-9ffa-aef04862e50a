/* Licence
 * Company: MCUSTUDIO
 * Auther: Ahypnis.
 * Version: V0.10
 * Time: 2025/06/05
 * Note:
 */

#include "mcu_cmic_gd32f470vet6.h"
#include "adc_app.h"
#include "rtc_app.h"
#include "system_state.h"

extern uint16_t adc_value[1];

/**
 * @brief 使用类似printf的方式显示字符串，显示6x8像素ASCII字符
 * @param x  X轴字符位置，范围：0-127
 * @param y  Y轴字符位置，范围：0-3
 * 例如：oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // 临时存储格式化后的字符串
  va_list arg;      // 可变参数列表
  int len;          // 返回字符串长度

  va_start(arg, format);
  // 安全地格式化字符串到buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

void oled_task(void)
{
  char time_str[32];

  // 第0行：显示电压值(保留2位小数)
  oled_printf(0, 0, "电压: %.2fV", adc_get_voltage());

  // 第1行：显示实时时间(YYYY-MM-DD HH:MM:SS格式)
  if (rtc_time_format(time_str, sizeof(time_str)) > 0)
  {
    oled_printf(0, 1, "%s", time_str);
  }
  else
  {
    oled_printf(0, 1, "时间: --:--:--");
  }

  // 第2行：显示采样频率
  oled_printf(0, 2, "频率: %s", system_frequency_to_string(system_frequency_get()));

  // 第3行：显示工作模式
  oled_printf(0, 3, "模式: %s", system_mode_to_string(system_mode_get()));
}

/* CUSTOM EDIT */
