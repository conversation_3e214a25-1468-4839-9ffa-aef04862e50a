/* Licence
 * Company: MCUSTUDIO
 * Auther: Ahypnis.
 * Version: V0.10
 * Time: 2025/06/05
 * Note:
 */

#include "mcu_cmic_gd32f470vet6.h"
#include "adc_app.h"
#include "rtc_app.h"
#include "system_state.h"
#include <string.h>

extern uint16_t adc_value[1];

/**
 * @brief 使用类似printf的方式显示字符串，显示6x8像素ASCII字符
 * @param x  X轴字符位置，范围：0-127
 * @param y  Y轴字符位置，范围：0-3
 * 例如：oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // 临时存储格式化后的字符串
  va_list arg;      // 可变参数列表
  int len;          // 返回字符串长度

  va_start(arg, format);
  // 安全地格式化字符串到buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

void oled_task(void)
{
  char time_str[32];
  char display_buffer[4][16];      // 4行显示缓存，每行最多15字符+结束符
  static char last_display[4][16]; // 上次显示内容
  static uint32_t last_update = 0;
  static uint8_t init_flag = 0;
  uint32_t current_time = get_system_ms();
  uint8_t need_update = 0;

  // 每500ms更新一次显示，减少刷新频率
  if (current_time - last_update < 500)
  {
    return;
  }
  last_update = current_time;

  // 准备显示内容到缓存
  // 第0行：电压值
  snprintf(display_buffer[0], sizeof(display_buffer[0]), "V:%.2fV", adc_get_voltage());

  // 第1行：时间
  if (rtc_time_format(time_str, sizeof(time_str)) > 0)
  {
    char *time_part = strstr(time_str, " ");
    if (time_part && strlen(time_part) >= 9)
    {
      snprintf(display_buffer[1], sizeof(display_buffer[1]), "T:%s", time_part + 1);
    }
    else
    {
      snprintf(display_buffer[1], sizeof(display_buffer[1]), "T:--:--:--");
    }
  }
  else
  {
    snprintf(display_buffer[1], sizeof(display_buffer[1]), "T:--:--:--");
  }

  // 第2行：频率
  sample_freq_t freq = system_frequency_get();
  if (freq == SAMPLE_FREQ_10HZ)
  {
    snprintf(display_buffer[2], sizeof(display_buffer[2]), "F:10Hz");
  }
  else
  {
    snprintf(display_buffer[2], sizeof(display_buffer[2]), "F:100Hz");
  }

  // 第3行：模式
  system_mode_t mode = system_mode_get();
  switch (mode)
  {
  case SYSTEM_MODE_COLLECT:
    snprintf(display_buffer[3], sizeof(display_buffer[3]), "M:COL");
    break;
  case SYSTEM_MODE_STORE:
    snprintf(display_buffer[3], sizeof(display_buffer[3]), "M:STO");
    break;
  case SYSTEM_MODE_PAUSE:
    snprintf(display_buffer[3], sizeof(display_buffer[3]), "M:PAU");
    break;
  default:
    snprintf(display_buffer[3], sizeof(display_buffer[3]), "M:ERR");
    break;
  }

  // 检查是否需要更新显示(首次运行或内容有变化)
  if (!init_flag)
  {
    need_update = 1;
    init_flag = 1;
    OLED_Clear(); // 只在初始化时清屏一次
  }
  else
  {
    for (int i = 0; i < 4; i++)
    {
      if (strcmp(display_buffer[i], last_display[i]) != 0)
      {
        need_update = 1;
        break;
      }
    }
  }

  // 只有内容变化时才更新显示
  if (need_update)
  {
    for (int i = 0; i < 4; i++)
    {
      // 只更新变化的行，避免全屏刷新
      if (strcmp(display_buffer[i], last_display[i]) != 0)
      {
        // 清除该行(用空格覆盖)
        oled_printf(0, i, "               "); // 15个空格清除行
        // 显示新内容
        oled_printf(0, i, "%s", display_buffer[i]);
        // 保存当前内容
        strcpy(last_display[i], display_buffer[i]);
      }
    }
  }
}

/* CUSTOM EDIT */
