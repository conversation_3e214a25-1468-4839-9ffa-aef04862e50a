File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,18.486971%,9875,96,9324,551,0,96
sdio_sdcard.o,12.724651%,6797,61,6792,0,5,56
ff.o,10.530553%,5625,6,5610,15,0,6
oled.o,6.297739%,3364,22,1270,2072,22,0
sd_app.o,4.135465%,2209,1957,1594,615,0,1957
btod.o,3.624382%,1936,0,1936,0,0,0
ebtn.o,3.141381%,1678,60,1678,0,0,60
mcu_cmic_gd32f470vet6.o,2.939194%,1570,576,1568,0,2,574
gd25qxx.o,2.345739%,1253,0,938,315,0,0
_printf_fp_dec.o,1.969447%,1052,0,1052,0,0,0
perf_counter.o,1.699865%,908,60,896,4,8,52
_scanf.o,1.654935%,884,0,884,0,0,0
gd32f4xx_rcu.o,1.512655%,808,0,800,8,0,0
_printf_fp_hex.o,1.501423%,802,0,764,38,0,0
gd32f4xx_dma.o,1.194399%,638,0,638,0,0,0
gd32f4xx_sdio.o,1.138236%,608,0,608,0,0,0
system_gd32f4xx.o,1.033398%,552,4,548,0,4,0
btn_app.o,1.025910%,548,196,338,14,196,0
gd32f4xx_timer.o,1.007189%,538,0,538,0,0,0
gd32f4xx_usart.o,0.995956%,532,0,532,0,0,0
gd32f4xx_adc.o,0.973491%,520,0,520,0,0,0
startup_gd32f450_470.o,0.921072%,492,2048,64,428,0,2048
usart_app.o,0.879886%,470,515,402,68,0,515
rtc_app.o,0.876142%,468,0,468,0,0,0
gd32f4xx_rtc.o,0.816235%,436,0,436,0,0,0
gd32f4xx_i2c.o,0.812491%,434,0,434,0,0,0
__printf_flags_ss_wp.o,0.765688%,409,0,392,17,0,0
bigflt0.o,0.703909%,376,0,228,148,0,0
oled_app.o,0.673955%,360,4,314,46,0,4
_scanf_int.o,0.621537%,332,0,332,0,0,0
lc_ctype_c.o,0.591583%,316,0,44,272,0,0
gd32f4xx_gpio.o,0.527932%,282,0,282,0,0,0
fz_wm.l,0.509211%,272,0,272,0,0,0
lludivv7m.o,0.449304%,240,0,240,0,0,0
diskio.o,0.441815%,236,0,236,0,0,0
adc_app.o,0.393141%,210,16,210,0,0,16
led_app.o,0.383780%,205,7,204,0,1,6
_printf_wctomb.o,0.366931%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.351954%,188,0,148,40,0,0
gd32f4xx_dac.o,0.340722%,182,0,182,0,0,0
_printf_intcommon.o,0.333233%,178,0,178,0,0,0
scheduler.o,0.322001%,172,85,88,0,84,1
gd32f4xx_misc.o,0.303280%,162,0,162,0,0,0
gd32f4xx_it.o,0.299536%,160,0,160,0,0,0
fnaninf.o,0.262094%,140,0,140,0,0,0
rt_memcpy_v6.o,0.258350%,138,0,138,0,0,0
lludiv10.o,0.258350%,138,0,138,0,0,0
system_state.o,0.252733%,135,4,134,0,1,3
_printf_fp_infnan.o,0.239629%,128,0,128,0,0,0
strcmpv7em.o,0.232140%,124,0,124,0,0,0
_printf_longlong_dec.o,0.232140%,124,0,124,0,0,0
perfc_port_default.o,0.228396%,122,0,122,0,0,0
_printf_dec.o,0.224652%,120,0,120,0,0,0
systick.o,0.220908%,118,4,118,0,0,4
llsdiv.o,0.217163%,116,0,116,0,0,0
_printf_oct_int_ll.o,0.209675%,112,0,112,0,0,0
gd32f4xx_spi.o,0.190954%,102,0,102,0,0,0
rt_memcpy_w.o,0.187210%,100,0,100,0,0,0
__scatter.o,0.175977%,94,0,94,0,0,0
memcmp.o,0.164745%,88,0,88,0,0,0
f2d.o,0.161000%,86,0,86,0,0,0
main.o,0.161000%,86,0,86,0,0,0
_printf_str.o,0.153512%,82,0,82,0,0,0
rt_memclr_w.o,0.146024%,78,0,78,0,0,0
_printf_pad.o,0.146024%,78,0,78,0,0,0
sys_stackheap_outer.o,0.138535%,74,0,74,0,0,0
strcpy.o,0.134791%,72,0,72,0,0,0
lc_numeric_c.o,0.134791%,72,0,44,28,0,0
_c16rtomb.o,0.134791%,72,0,72,0,0,0
rt_memclr.o,0.127303%,68,0,68,0,0,0
_sgetc.o,0.119814%,64,0,64,0,0,0
__2snprintf.o,0.119814%,64,0,64,0,0,0
strlen.o,0.116070%,62,0,62,0,0,0
vsnprintf.o,0.112326%,60,0,60,0,0,0
__0sscanf.o,0.112326%,60,0,60,0,0,0
__dczerorl.o,0.108582%,58,0,58,0,0,0
m_wm.l,0.089861%,48,0,48,0,0,0
fpclassify.o,0.089861%,48,0,48,0,0,0
_printf_char_common.o,0.089861%,48,0,48,0,0,0
scanf_char.o,0.082372%,44,0,44,0,0,0
init_aeabi.o,0.082372%,44,0,44,0,0,0
_printf_wchar.o,0.082372%,44,0,44,0,0,0
_printf_char.o,0.082372%,44,0,44,0,0,0
_printf_charcount.o,0.074884%,40,0,40,0,0,0
libinit2.o,0.071140%,38,0,38,0,0,0
_printf_truncate.o,0.067396%,36,0,36,0,0,0
_chval.o,0.052419%,28,0,28,0,0,0
__scatter_zi.o,0.052419%,28,0,28,0,0,0
systick_wrapper_gnu.o,0.052419%,28,0,28,0,0,0
fpinit.o,0.048675%,26,0,26,0,0,0
strchr.o,0.037442%,20,0,20,0,0,0
isspace.o,0.033698%,18,0,18,0,0,0
exit.o,0.033698%,18,0,18,0,0,0
gd32f4xx_pmu.o,0.033698%,18,0,18,0,0,0
rt_ctype_table.o,0.029954%,16,0,16,0,0,0
_snputc.o,0.029954%,16,0,16,0,0,0
__printf_wp.o,0.026209%,14,0,14,0,0,0
dretinf.o,0.022465%,12,0,12,0,0,0
sys_exit.o,0.022465%,12,0,12,0,0,0
__rtentry2.o,0.022465%,12,0,12,0,0,0
rtexit2.o,0.018721%,10,0,10,0,0,0
_printf_ll.o,0.018721%,10,0,10,0,0,0
_printf_l.o,0.018721%,10,0,10,0,0,0
rt_locale_intlibspace.o,0.014977%,8,0,8,0,0,0
libspace.o,0.014977%,8,96,8,0,0,96
__main.o,0.014977%,8,0,8,0,0,0
heapauxi.o,0.011233%,6,0,6,0,0,0
_printf_x.o,0.011233%,6,0,6,0,0,0
_printf_u.o,0.011233%,6,0,6,0,0,0
_printf_s.o,0.011233%,6,0,6,0,0,0
_printf_p.o,0.011233%,6,0,6,0,0,0
_printf_o.o,0.011233%,6,0,6,0,0,0
_printf_n.o,0.011233%,6,0,6,0,0,0
_printf_ls.o,0.011233%,6,0,6,0,0,0
_printf_llx.o,0.011233%,6,0,6,0,0,0
_printf_llu.o,0.011233%,6,0,6,0,0,0
_printf_llo.o,0.011233%,6,0,6,0,0,0
_printf_lli.o,0.011233%,6,0,6,0,0,0
_printf_lld.o,0.011233%,6,0,6,0,0,0
_printf_lc.o,0.011233%,6,0,6,0,0,0
_printf_i.o,0.011233%,6,0,6,0,0,0
_printf_g.o,0.011233%,6,0,6,0,0,0
_printf_f.o,0.011233%,6,0,6,0,0,0
_printf_e.o,0.011233%,6,0,6,0,0,0
_printf_d.o,0.011233%,6,0,6,0,0,0
_printf_c.o,0.011233%,6,0,6,0,0,0
_printf_a.o,0.011233%,6,0,6,0,0,0
__rtentry4.o,0.011233%,6,0,6,0,0,0
printf2.o,0.007488%,4,0,4,0,0,0
printf1.o,0.007488%,4,0,4,0,0,0
_printf_percent_end.o,0.007488%,4,0,4,0,0,0
use_no_semi.o,0.003744%,2,0,2,0,0,0
rtexit.o,0.003744%,2,0,2,0,0,0
libshutdown2.o,0.003744%,2,0,2,0,0,0
libshutdown.o,0.003744%,2,0,2,0,0,0
libinit.o,0.003744%,2,0,2,0,0,0
