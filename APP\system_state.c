/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/11
* Note: 系统状态管理模块实现文件
*/

#include "system_state.h"

// 全局系统状态变量
static system_state_t g_system_state = {
    .mode = SYSTEM_MODE_COLLECT,    // 默认采集模式
    .frequency = SAMPLE_FREQ_10HZ,  // 默认10Hz频率
    .mode_changed = 0,              // 模式未变化
    .freq_changed = 0               // 频率未变化
};

/**
 * @brief 系统状态初始化
 */
void system_state_init(void)
{
    g_system_state.mode = SYSTEM_MODE_COLLECT;
    g_system_state.frequency = SAMPLE_FREQ_10HZ;
    g_system_state.mode_changed = 0;
    g_system_state.freq_changed = 0;
}

/**
 * @brief 获取当前工作模式
 * @return 当前工作模式
 */
system_mode_t system_mode_get(void)
{
    return g_system_state.mode;
}

/**
 * @brief 设置工作模式
 * @param mode 要设置的工作模式
 */
void system_mode_set(system_mode_t mode)
{
    if (mode != g_system_state.mode) {
        g_system_state.mode = mode;
        g_system_state.mode_changed = 1;  // 标记模式已变化
    }
}

/**
 * @brief 获取当前采样频率
 * @return 当前采样频率
 */
sample_freq_t system_frequency_get(void)
{
    return g_system_state.frequency;
}

/**
 * @brief 设置采样频率
 * @param freq 要设置的采样频率
 */
void system_frequency_set(sample_freq_t freq)
{
    if (freq != g_system_state.frequency) {
        g_system_state.frequency = freq;
        g_system_state.freq_changed = 1;  // 标记频率已变化
    }
}

/**
 * @brief 检查模式是否变化
 * @return 1-模式已变化，0-模式未变化
 */
uint8_t system_mode_is_changed(void)
{
    return g_system_state.mode_changed;
}

/**
 * @brief 检查频率是否变化
 * @return 1-频率已变化，0-频率未变化
 */
uint8_t system_frequency_is_changed(void)
{
    return g_system_state.freq_changed;
}

/**
 * @brief 清除变化标志
 */
void system_state_clear_flags(void)
{
    g_system_state.mode_changed = 0;
    g_system_state.freq_changed = 0;
}

/**
 * @brief 工作模式转字符串
 * @param mode 工作模式
 * @return 模式字符串
 */
const char* system_mode_to_string(system_mode_t mode)
{
    switch (mode) {
        case SYSTEM_MODE_COLLECT:
            return "采集模式";
        case SYSTEM_MODE_STORE:
            return "存储模式";
        case SYSTEM_MODE_PAUSE:
            return "暂停模式";
        default:
            return "未知模式";
    }
}

/**
 * @brief 采样频率转字符串
 * @param freq 采样频率
 * @return 频率字符串
 */
const char* system_frequency_to_string(sample_freq_t freq)
{
    switch (freq) {
        case SAMPLE_FREQ_10HZ:
            return "10Hz";
        case SAMPLE_FREQ_100HZ:
            return "100Hz";
        default:
            return "未知频率";
    }
}
