/* Licence
 * Company: MCUSTUDIO
 * Auther: Ahypnis.
 * Version: V0.10
 * Time: 2025/06/05
 * Note:
 */
#include "mcu_cmic_gd32f470vet6.h"
#include "rtc_app.h"
#include <string.h>
#include <stdio.h>
#include <ctype.h>

__IO uint16_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[512] = {0};

int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;
    // ��ʼ���ɱ�����б�
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    for (tx_count = 0; tx_count < len; tx_count++)
    {
        usart_data_transmit(usart_periph, buffer[tx_count]);
        while (RESET == usart_flag_get(usart_periph, USART_FLAG_TBE))
            ;
    }

    return len;
}

/**
 * @brief 验证TIME指令格式的严格性(检查补零格式)
 * @param cmd_str 指令字符串
 * @return 0-格式正确，-1-格式错误
 */
static int validate_time_format(const char *cmd_str)
{
    // 检查指令长度：TIME=YYYY-MM-DD HH:MM:SS (24字符)
    if (strlen(cmd_str) != 24)
    {
        return -1;
    }

    // 检查固定位置的字符
    if (cmd_str[4] != '=' || cmd_str[9] != '-' || cmd_str[12] != '-' ||
        cmd_str[15] != ' ' || cmd_str[18] != ':' || cmd_str[21] != ':')
    {
        return -1;
    }

    // 检查所有数字位置是否为数字
    for (int i = 0; i < 24; i++)
    {
        if (i == 4 || i == 9 || i == 12 || i == 15 || i == 18 || i == 21)
        {
            continue; // 跳过分隔符
        }
        if (!isdigit(cmd_str[i]))
        {
            return -1;
        }
    }

    return 0; // 格式正确
}

/**
 * @brief 解析TIME=指令并设置RTC时间
 * @param cmd_str 指令字符串
 * @return 0-成功，-1-格式错误，-2-时间范围错误，-3-RTC设置失败
 */
static int parse_time_command(const char *cmd_str)
{
    int year, month, date, hour, minute, second;

    // 首先验证格式严格性
    if (validate_time_format(cmd_str) != 0)
    {
        return -1; // 格式错误
    }

    // 解析时间数值：TIME=YYYY-MM-DD HH:MM:SS
    if (sscanf(cmd_str, "TIME=%d-%d-%d %d:%d:%d",
               &year, &month, &date, &hour, &minute, &second) != 6)
    {
        return -1; // 解析失败
    }

    // 验证时间范围
    if (year < 2000 || year > 2099 || month < 1 || month > 12 ||
        date < 1 || date > 31 || hour < 0 || hour > 23 ||
        minute < 0 || minute > 59 || second < 0 || second > 59)
    {
        return -2; // 时间范围错误
    }

    // 简单的月份天数验证
    if ((month == 2 && date > 29) ||
        ((month == 4 || month == 6 || month == 9 || month == 11) && date > 30))
    {
        return -2; // 日期错误
    }

    // 转换年份为两位数 (2025 -> 25)
    uint8_t rtc_year = (uint8_t)(year - 2000);

    // 调用RTC设置函数
    if (rtc_time_set(rtc_year, (uint8_t)month, (uint8_t)date,
                     (uint8_t)hour, (uint8_t)minute, (uint8_t)second) != 0)
    {
        return -3; // RTC设置失败
    }

    return 0; // 设置成功
}

void uart_task(void)
{
    static uint32_t time_cmd_start = 0; // TIME指令接收时间

    if (!rx_flag)
        return;

    // 检查是否为TIME=指令
    if (strncmp((char *)uart_dma_buffer, "TIME=", 5) == 0)
    {
        // 记录指令处理开始时间
        time_cmd_start = get_system_ms();

        // 解析TIME=指令
        int result = parse_time_command((char *)uart_dma_buffer);

        // 计算处理时间
        uint32_t process_time = get_system_ms() - time_cmd_start;

        switch (result)
        {
        case 0: // 设置成功
            my_printf(DEBUG_USART, "RTC时间设置成功，处理时间：%ldms\r\n", process_time);
            // 显示设置后的时间进行确认
            char time_str[32];
            if (rtc_time_format(time_str, sizeof(time_str)) > 0)
            {
                my_printf(DEBUG_USART, "当前时间：%s\r\n", time_str);
            }
            break;
        case -1: // 格式错误
            my_printf(DEBUG_USART, "错误：TIME指令格式不正确\r\n");
            my_printf(DEBUG_USART, "正确格式：TIME=YYYY-MM-DD HH:MM:SS\r\n");
            my_printf(DEBUG_USART, "示例：TIME=2025-05-30 15:00:00\r\n");
            my_printf(DEBUG_USART, "注意：日期时间必须补零\r\n");
            break;
        case -2: // 时间范围错误
            my_printf(DEBUG_USART, "错误：时间数值超出有效范围\r\n");
            my_printf(DEBUG_USART, "年份：2000-2099，月份：01-12，日期：01-31\r\n");
            my_printf(DEBUG_USART, "时间：00:00:00-23:59:59\r\n");
            break;
        case -3: // RTC设置失败
            my_printf(DEBUG_USART, "错误：RTC硬件设置失败\r\n");
            break;
        default:
            my_printf(DEBUG_USART, "错误：未知错误\r\n");
            break;
        }

        // 检查是否在5秒内完成
        if (process_time > 5000)
        {
            my_printf(DEBUG_USART, "警告：处理时间超过5秒要求\r\n");
        }
    }
    else
    {
        // 回显接收到的数据
        my_printf(DEBUG_USART, "接收到：%s", uart_dma_buffer);
    }

    // 清空接收缓冲区
    memset(uart_dma_buffer, 0, 256);
    rx_flag = 0; // 清除接收标志
}
